:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.6 0.1 240);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.9 0.05 240);
  --secondary-foreground: oklch(0.15 0 0);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.4 0 0);
  --accent: oklch(0.8 0.1 240);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.6 0.2 20);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: oklch(0.6 0.1 240);
  --chart-1: oklch(0.6 0.1 240);
  --chart-2: oklch(0.7 0.15 240);
  --chart-3: oklch(0.8 0.1 240);
  --chart-4: oklch(0.9 0.05 240);
  --chart-5: oklch(0.95 0 0);
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.15 0 0);
  --sidebar-primary: oklch(0.6 0.1 240);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.8 0.1 240);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.9 0 0);
  --sidebar-ring: oklch(0.6 0.1 240);
  --font-sans: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-serif: Playfair Display, Georgia, 'Times New Roman', Times, serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Source Code Pro', 'IBM Plex Mono', 'Roboto Mono', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0 hsl(0 0% 0% / 0.1);
  --shadow-sm: 0 2px 4px 0 hsl(0 0% 0% / 0.1);
  --shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.06);
  --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 10px 15px -3px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -2px hsl(0 0% 0% / 0.05);
  --shadow-xl: 0 20px 25px -5px hsl(0 0% 0% / 0.1), 0 10px 10px -5px hsl(0 0% 0% / 0.04);
  --shadow-2xl: 0 25px 50px -12px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --radius-sm: calc(var(--radius) - 0.125rem);
  --radius-md: calc(var(--radius) - 0.0625rem);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 0.125rem);
}
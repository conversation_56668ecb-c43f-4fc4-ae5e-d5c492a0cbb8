@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));
@config "./tailwind.config.js";

/* behindyourbrain Blog - Custom Styles */

/* Font Loading Optimization - Prevent Layout Shifts */
/* Optimized font loading - only essential weights and latin subset */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyeMZhrib2Bg-4.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyeMZhrib2Bg-4.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Playfair Display';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qO0isEw.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Ensure consistent font metrics to prevent layout shift */
.font-serif {
  font-family: 'Playfair Display', Georgia, 'Times New Roman', serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
}

.font-sans {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
}

/*
 * Centralized Color System
 * Colors are now managed through src/config/colorSchemes.ts
 * This section will be dynamically updated by the theme system
 */
:root {
  color-scheme: light;
  --radius: 0.5rem;

  /* Core colors - Light theme defaults */
  --background: 248 249 250;
  --foreground: 26 32 44;
  --card: 255 255 255;
  --card-foreground: 26 32 44;
  --popover: 255 255 255;
  --popover-foreground: 26 32 44;
  --primary: 0 123 255;
  --primary-foreground: 247 250 252;
  --secondary: 108 117 125;
  --secondary-foreground: 26 32 44;
  --muted: 248 249 250;
  --muted-foreground: 108 117 125;
  --accent: 202 66 70;
  --accent-foreground: 247 250 252;
  --destructive: 220 38 38;
  --destructive-foreground: 248 249 250;
  --border: 229 231 235;
  --input: 229 231 235;
  --ring: 0 123 255;

  /* Chart colors */
  --chart-1: 0 123 255;
  --chart-2: 202 66 70;
  --chart-3: 108 117 125;
  --chart-4: 34 197 94;
  --chart-5: 168 85 247;

  /* Sidebar colors */
  --sidebar: 26 32 44;
  --sidebar-foreground: 247 250 252;
  --sidebar-primary: 0 123 255;
  --sidebar-primary-foreground: 247 250 252;
  --sidebar-accent: 45 55 72;
  --sidebar-accent-foreground: 247 250 252;
  --sidebar-border: 74 85 104;
  --sidebar-ring: 0 123 255;

  /* Accessibility and focus colors */
  --focus-ring: var(--ring);
  --focus-ring-offset: var(--background);
  --selection-bg: var(--accent);
  --selection-text: var(--accent-foreground);
  --high-contrast-outline: 0 0 0;
  --high-contrast-bg: 255 255 0;
  --high-contrast-text: 0 0 0;
}

.dark {
  color-scheme: dark;

  /* Core colors - Dark theme */
  --background: 26 32 44;
  --foreground: 247 250 252;
  --card: 45 55 72;
  --card-foreground: 247 250 252;
  --popover: 45 55 72;
  --popover-foreground: 247 250 252;
  --primary: 0 123 255;
  --primary-foreground: 247 250 252;
  --secondary: 74 85 104;
  --secondary-foreground: 247 250 252;
  --muted: 45 55 72;
  --muted-foreground: 156 163 175;
  --accent: 202 66 70;
  --accent-foreground: 247 250 252;
  --destructive: 239 68 68;
  --destructive-foreground: 247 250 252;
  --border: 74 85 104;
  --input: 74 85 104;
  --ring: 0 123 255;

  /* Chart colors */
  --chart-1: 0 123 255;
  --chart-2: 202 66 70;
  --chart-3: 108 117 125;
  --chart-4: 34 197 94;
  --chart-5: 168 85 247;

  /* Sidebar colors */
  --sidebar: 26 32 44;
  --sidebar-foreground: 247 250 252;
  --sidebar-primary: 0 123 255;
  --sidebar-primary-foreground: 247 250 252;
  --sidebar-accent: 45 55 72;
  --sidebar-accent-foreground: 247 250 252;
  --sidebar-border: 74 85 104;
  --sidebar-ring: 0 123 255;

  /* Accessibility and focus colors for dark mode */
  --focus-ring: var(--ring);
  --focus-ring-offset: var(--background);
  --selection-bg: var(--accent);
  --selection-text: var(--accent-foreground);
  --high-contrast-outline: 255 255 255;
  --high-contrast-bg: 0 0 0;
  --high-contrast-text: 255 255 255;
}

/* Ensure full page theme coverage */
html {
  background-color: rgb(var(--background));
  transition: background-color 0.2s ease;
}

html.dark {
  background-color: rgb(var(--background));
}

body {
  background-color: inherit;
}

/* Smooth scrolling with performance optimizations */
html {
  scroll-behavior: smooth;
  /* Optimize scrolling performance */
  -webkit-overflow-scrolling: touch;
  /* Prevent overscroll bounce on supported browsers */
  overscroll-behavior: none;
}

/* Performance optimizations for all elements */
* {
  /* Optimize box-sizing for better layout performance */
  box-sizing: border-box;
}

/* Optimize scrolling container performance */
.scroll-container {
  /* Enable hardware acceleration */
  will-change: scroll-position;
  /* Optimize scrolling on iOS */
  -webkit-overflow-scrolling: touch;
  /* Prevent overscroll bounce */
  overscroll-behavior: none;
}

/* Optimize main content areas */
main {
  /* Enable containment for better performance */
  contain: layout style paint;
  /* Hardware acceleration */
  transform: translateZ(0);
}

/* Hardware acceleration for commonly animated elements */
.transition-transform,
.hover\\:scale-105:hover,
.hover\\:scale-110:hover,
.group:hover .group-hover\\:scale-105,
.group:hover .group-hover\\:scale-110 {
  will-change: transform;
  transform: translateZ(0);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--muted));
}

::-webkit-scrollbar-thumb {
  @apply bg-accent rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-accent-dark;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar {
  width: 6px;
}

.dark ::-webkit-scrollbar-track {
  background: rgb(var(--muted));
}

.dark ::-webkit-scrollbar-thumb {
  @apply bg-accent rounded;
}

.dark ::-webkit-scrollbar-thumb:hover {
  @apply bg-accent-dark;
}

/* Typography enhancements */
.font-serif {
  font-feature-settings: "liga" 1, "kern" 1;
}

/* Image hover effects - Optimized for performance */
.image-hover-effect {
  /* Use specific properties instead of 'all' for better performance */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Hardware acceleration hint */
  will-change: transform;
  /* Force GPU layer creation */
  transform: translateZ(0);
}

.image-hover-effect:hover {
  /* Use transform3d for hardware acceleration */
  transform: scale3d(1.02, 1.02, 1) translateZ(0);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced Focus styles for mobile accessibility */
.focus-visible:focus {
  @apply outline-accent outline-2 outline-offset-2;
}

/* Mobile-specific focus styles */
@media (max-width: 768px) {
  button:focus,
  a:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 3px solid rgb(var(--focus-ring));
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Enhanced focus for interactive elements */
  .mobile-focus:focus {
    outline: 3px solid rgb(var(--focus-ring));
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgb(var(--focus-ring) / 0.2);
  }
}

/* Selection color */
::selection {
  background: rgb(var(--selection-bg));
  color: rgb(var(--selection-text));
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-accent to-accent-dark text-transparent bg-clip-text;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark mode card hover */
.dark .card-hover:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgb(var(--background) / 0.95);
  }

  .dark .backdrop-blur-md {
    background-color: rgb(var(--background) / 0.95);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Mobile-First Base Styles */
html {
  /* Mobile-first scrolling optimizations */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  touch-action: manipulation;
  /* Prevent zoom on orientation change */
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  /* Mobile-first body optimizations */
  overscroll-behavior-y: none;
  touch-action: pan-y;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* Ensure minimum font size for readability */
  font-size: 16px;
  line-height: 1.6;
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
  /* Additional mobile-only optimizations */

  /* Optimize mobile card interactions for better performance */
  .mobile-card,
  .group {
    /* Prevent layout thrashing on mobile */
    contain: layout style paint;
  }

  /* Optimize mobile images */
  img {
    /* Prevent layout shifts */
    height: auto;
    /* Optimize rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  /* Mobile-optimized containers */
  .mobile-container {
    padding-left: 16px;
    padding-right: 16px;
    max-width: 100%;
  }

  /* Edge-to-edge mobile reading experience */
  .mobile-edge-to-edge {
    margin-left: calc(-1 * var(--mobile-padding, 1rem));
    margin-right: calc(-1 * var(--mobile-padding, 1rem));
    padding-left: var(--mobile-padding, 1rem);
    padding-right: var(--mobile-padding, 1rem);
  }

  /* Fluid mobile containers - no max-width constraints */
  .mobile-fluid-container {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }

  /* Mobile reading content - edge-to-edge with minimal padding */
  .mobile-reading-content {
    padding-left: 1rem;
    padding-right: 1rem;
    margin-left: 0;
    margin-right: 0;
    max-width: none;
  }

  /* Remove card styling on mobile for fluid experience */
  .mobile-no-card {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  /* Enhanced mobile card interactions - Performance optimized */
  .mobile-card {
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    /* Optimize transitions for specific properties */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    /* Hardware acceleration */
    will-change: transform;
    transform: translateZ(0);
  }

  .mobile-card:active {
    /* Use transform3d for hardware acceleration */
    transform: scale3d(0.98, 0.98, 1) translateZ(0);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Mobile navigation improvements - Performance optimized */
  .mobile-nav {
    position: sticky;
    top: 0;
    z-index: 50;
    /* Optimize backdrop filter for better performance */
    backdrop-filter: blur(8px);
    background-color: rgba(255, 255, 255, 0.95);
    /* Hardware acceleration for sticky positioning */
    will-change: transform;
    transform: translateZ(0);
  }

  .dark .mobile-nav {
    background-color: rgba(26, 32, 44, 0.95);
  }

  /* Mobile menu button */
  .mobile-menu-button {
    padding: 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .mobile-menu-button:active {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.95);
  }

  /* Mobile dropdown menu */
  .mobile-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 60;
  }

  .dark .mobile-dropdown {
    background-color: #1a202c;
  }

  /* Mobile text selection */
  .mobile-selectable {
    -webkit-user-select: text;
    user-select: text;
    -webkit-touch-callout: default;
  }

  /* Mobile image optimization */
  .mobile-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    object-fit: cover;
  }

  /* Mobile spacing utilities */
  .mobile-spacing-sm {
    margin: 8px 0;
  }

  .mobile-spacing-md {
    margin: 16px 0;
  }

  .mobile-spacing-lg {
    margin: 24px 0;
  }

  /* Mobile grid improvements */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 16px;
  }

  /* Mobile PostCard fluid styling */
  .mobile-post-card {
    border-left: none !important;
    border-right: none !important;
    border-radius: 0 !important;
    margin-left: -1rem;
    margin-right: -1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    background: rgb(var(--background));
  }

  /* Mobile post card with minimal borders */
  .mobile-post-card-minimal {
    border: none !important;
    border-bottom: 1px solid rgb(var(--border)) !important;
    border-radius: 0 !important;
    background: transparent;
    box-shadow: none !important;
  }

  /* Mobile hero image - full width */
  .mobile-hero-image {
    margin-left: -1rem;
    margin-right: -1rem;
    border-radius: 0 !important;
    width: calc(100% + 2rem);
  }

  /* Mobile content spacing */
  .mobile-content-spacing {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  /* Mobile accessibility improvements */
  .mobile-accessible {
    font-size: 16px;
    line-height: 1.5;
    color: #1f2937;
    font-weight: 400;
  }

  .dark .mobile-accessible {
    color: #f9fafb;
  }

  /* Mobile loading states */
  .mobile-loading {
    padding: 40px 20px;
    text-align: center;
  }

  .mobile-spinner {
    width: 32px;
    height: 32px;
    margin: 0 auto 16px;
  }
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:vertical-text {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
}

/* Mobile-first responsive text sizing and touch targets */
@media (max-width: 639px) {
  .mobile-text-adjust {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

/* Mobile touch target optimization */
@media (max-width: 768px) {
  /* Minimum 44px touch targets */
  button,
  a,
  input[type="button"],
  input[type="submit"],
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
  }

  /* Enhanced spacing for mobile navigation */
  nav a,
  nav button {
    padding: 16px 20px;
    margin: 4px 0;
  }

  /* Mobile card spacing */
  .mobile-card-spacing {
    margin-bottom: 24px;
    padding: 20px;
  }

  /* Mobile typography improvements */
  h1 {
    font-size: 2rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  h2 {
    font-size: 1.75rem;
    line-height: 1.3;
    margin-bottom: 0.875rem;
  }

  h3 {
    font-size: 1.5rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }

  p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  /* Improved text contrast for mobile */
  .mobile-text-contrast {
    color: #1f2937;
    font-weight: 500;
  }

  .dark .mobile-text-contrast {
    color: #f9fafb;
  }

  /* Mobile button improvements */
  .mobile-button {
    font-size: 1rem;
    font-weight: 600;
    padding: 14px 24px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .mobile-button:active {
    transform: scale(0.98);
  }

  /* Mobile link improvements */
  .mobile-link {
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 4px;
    padding: 8px 4px;
  }

  /* Mobile-optimized form elements */
  input,
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 16px 20px;
    border-radius: 12px;
    border: 2px solid rgb(var(--border));
    background-color: rgb(var(--background));
    color: rgb(var(--foreground));
    min-height: 44px;
    transition: all 0.2s ease;
  }

  input:focus,
  textarea:focus,
  select:focus {
    border-color: rgb(var(--primary));
    box-shadow: 0 0 0 4px rgb(var(--primary) / 0.1);
    outline: none;
  }

  /* Mobile button improvements */
  button,
  .btn {
    font-size: 16px;
    font-weight: 600;
    padding: 16px 24px;
    border-radius: 12px;
    min-height: 44px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    background-color: rgb(var(--primary));
    color: rgb(var(--primary-foreground));
  }

  button:hover,
  .btn:hover {
    background-color: rgb(var(--primary) / 0.9);
    transform: translateY(-1px);
  }

  button:active,
  .btn:active {
    transform: translateY(0);
  }

  /* Secondary button variant */
  .btn-secondary {
    background-color: rgb(var(--secondary));
    color: rgb(var(--secondary-foreground));
    border: 2px solid rgb(var(--border));
  }

  .btn-secondary:hover {
    background-color: rgb(var(--secondary) / 0.8);
  }
 }

/* Extra small mobile devices (phones in portrait) */
@media (max-width: 480px) {
  /* Enhanced typography for very small screens */
  body {
    font-size: 14px;
    line-height: 1.6;
  }

  h1 {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  h3 {
    font-size: 1.25rem;
    line-height: 1.4;
  }

  /* Larger touch targets for small screens */
  button,
  a,
  .touch-target {
    min-height: 48px;
    min-width: 48px;
    padding: 16px 20px;
  }

  /* Enhanced spacing for readability */
  .small-mobile-spacing {
    padding: 16px 12px;
    margin: 12px 0;
  }

  /* Improved card layout for small screens */
  .small-mobile-card {
    padding: 16px;
    margin: 12px 8px;
    border-radius: 8px;
  }

  /* Better text contrast for small screens */
  .small-mobile-text {
    font-size: 15px;
    line-height: 1.7;
    color: #111827;
    font-weight: 500;
  }

  .dark .small-mobile-text {
    color: #f3f4f6;
  }
}

/* Mobile landscape orientation */
@media (max-width: 768px) and (orientation: landscape) {
  /* Optimize for landscape mobile viewing */
  .landscape-mobile {
    padding: 12px 20px;
  }

  /* Adjust navigation for landscape */
  .landscape-nav {
    height: 60px;
    padding: 8px 16px;
  }

  /* Compact spacing for landscape */
  .landscape-spacing {
    margin: 8px 0;
    padding: 8px 12px;
  }
}

/* High contrast mode support for accessibility */
@media (prefers-contrast: high) {
  button,
  a,
  input {
    border: 2px solid currentColor;
  }

  .high-contrast-focus:focus {
    outline: 4px solid rgb(var(--high-contrast-outline));
    outline-offset: 2px;
    background-color: rgb(var(--high-contrast-bg));
    color: rgb(var(--high-contrast-text));
  }

  .dark .high-contrast-focus:focus {
    outline: 4px solid rgb(var(--high-contrast-outline));
    background-color: rgb(var(--high-contrast-bg));
    color: rgb(var(--high-contrast-text));
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .reduced-motion-safe {
    transform: none !important;
  }
}

/* Enhanced Content Styles */
.enhanced-content {
  @apply text-gray-900 dark:text-gray-100;
}

.enhanced-content h1,
.enhanced-content h2,
.enhanced-content h3,
.enhanced-content h4,
.enhanced-content h5,
.enhanced-content h6 {
  @apply font-semibold text-gray-900 dark:text-gray-100 mt-8 mb-4;
}

.enhanced-content h1 { @apply text-3xl; }
.enhanced-content h2 { @apply text-2xl; }
.enhanced-content h3 { @apply text-xl; }
.enhanced-content h4 { @apply text-lg; }
.enhanced-content h5 { @apply text-base; }
.enhanced-content h6 { @apply text-sm; }

.enhanced-content p {
  @apply mb-4 leading-relaxed;
}

.enhanced-content blockquote {
  @apply border-l-4 border-blue-500 pl-4 py-2 my-6 bg-blue-50 dark:bg-blue-900/20 italic;
}

.enhanced-content ul,
.enhanced-content ol {
  @apply mb-4 pl-6;
}

.enhanced-content ul {
  @apply list-disc;
}

.enhanced-content ol {
  @apply list-decimal;
}

.enhanced-content li {
  @apply mb-2;
}

.enhanced-content a {
  @apply text-blue-600 dark:text-blue-400 hover:underline;
}

.enhanced-content img {
  @apply max-w-full h-auto rounded-lg my-4;
}

.enhanced-content code {
  @apply bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono;
}

.enhanced-content pre {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto my-4;
}

.enhanced-content pre code {
  @apply bg-transparent p-0;
}

/* Table Styles */
.enhanced-content table {
  @apply w-full border-collapse my-6;
}

.enhanced-content th,
.enhanced-content td {
  @apply border border-gray-300 dark:border-gray-600 px-4 py-2 text-left;
}

.enhanced-content th {
  @apply bg-gray-100 dark:bg-gray-800 font-semibold;
}

.enhanced-content .table-responsive {
  @apply overflow-x-auto;
}

.enhanced-content .table-striped tbody tr:nth-child(odd) {
  @apply bg-gray-50 dark:bg-gray-800/50;
}

/* Callout Styles */
.enhanced-content .callout {
  @apply p-4 my-4 rounded-lg border-l-4;
}

.enhanced-content .callout-info {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-500 text-blue-900 dark:text-blue-100;
}

.enhanced-content .callout-warning {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500 text-yellow-900 dark:text-yellow-100;
}

.enhanced-content .callout-error {
  @apply bg-red-50 dark:bg-red-900/20 border-red-500 text-red-900 dark:text-red-100;
}

.enhanced-content .callout-success {
  @apply bg-green-50 dark:bg-green-900/20 border-green-500 text-green-900 dark:text-green-100;
}

/* Code Block Enhancements */
.code-block-container {
  @apply my-6 rounded-lg overflow-hidden shadow-lg;
}

.code-block-header {
  @apply bg-gray-800 text-gray-200 px-4 py-2 flex justify-between items-center text-sm;
}

.code-block {
  @apply bg-gray-900 text-gray-100 p-4 overflow-x-auto m-0 text-sm;
}

/* TinyMCE Dark Mode Support */
.tox .tox-edit-area__iframe {
  background-color: transparent !important;
}

.dark .tox {
  border-color: #4a5568 !important;
}

.dark .tox .tox-toolbar,
.dark .tox .tox-toolbar__primary,
.dark .tox .tox-toolbar__overflow {
  background-color: #2d3748 !important;
  border-color: #4a5568 !important;
}

.dark .tox .tox-tbtn {
  color: #e2e8f0 !important;
}

.dark .tox .tox-tbtn:hover {
  background-color: #4a5568 !important;
}

.dark .tox .tox-menubar {
  background-color: #2d3748 !important;
  border-color: #4a5568 !important;
}

.dark .tox .tox-menu {
  background-color: #2d3748 !important;
  border-color: #4a5568 !important;
}

.dark .tox .tox-collection__item {
  color: #e2e8f0 !important;
}

.dark .tox .tox-collection__item:hover {
  background-color: #4a5568 !important;
}

/* Enhanced Rich Text Editor Stability */
.rich-text-editor {
  position: relative;
  contain: layout style;
}

.rich-text-editor .tox {
  transition: opacity 0.2s ease;
}

/* Prevent layout shifts during editor initialization */
.rich-text-editor .tox-tinymce {
  min-height: 300px;
}

/* Improved focus management */
.rich-text-editor .tox-edit-area__iframe {
  scroll-behavior: auto !important;
}

/* Cross-browser compatibility improvements */
@supports (-webkit-appearance: none) {
  /* Safari-specific fixes */
  .rich-text-editor {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

@supports (-moz-appearance: none) {
  /* Firefox-specific fixes */
  .rich-text-editor {
    will-change: auto;
  }
}

/* Prevent viewport jumping on mobile */
@media (max-width: 768px) {
  .rich-text-editor .tox-edit-area__iframe {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus in iOS */
  .rich-text-editor input,
  .rich-text-editor textarea {
    font-size: 16px !important;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Responsive Container Utilities */
  .container-fluid {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .container-edge-to-edge {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }

  @media (min-width: 768px) {
    .container-fluid {
      max-width: 1280px;
      margin-left: auto;
      margin-right: auto;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Mobile-First Typography */
  .text-fluid-xs { font-size: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem); }
  .text-fluid-sm { font-size: clamp(0.875rem, 0.8rem + 0.375vw, 1rem); }
  .text-fluid-base { font-size: clamp(1rem, 0.9rem + 0.5vw, 1.125rem); }
  .text-fluid-lg { font-size: clamp(1.125rem, 1rem + 0.625vw, 1.25rem); }
  .text-fluid-xl { font-size: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem); }
  .text-fluid-2xl { font-size: clamp(1.5rem, 1.3rem + 1vw, 1.875rem); }
  .text-fluid-3xl { font-size: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem); }
  .text-fluid-4xl { font-size: clamp(2.25rem, 1.9rem + 1.75vw, 3rem); }

  /* Mobile-First Spacing */
  .space-fluid-xs { margin: clamp(0.5rem, 1vw, 0.75rem); }
  .space-fluid-sm { margin: clamp(0.75rem, 2vw, 1rem); }
  .space-fluid-md { margin: clamp(1rem, 3vw, 1.5rem); }
  .space-fluid-lg { margin: clamp(1.5rem, 4vw, 2rem); }
  .space-fluid-xl { margin: clamp(2rem, 5vw, 3rem); }

  /* Touch Target Optimization */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .touch-target-large {
    min-height: 48px;
    min-width: 48px;
  }

  /* Mobile-Specific Utilities */
  .mobile-only {
    @media (min-width: 768px) {
      display: none !important;
    }
  }

  .desktop-only {
    @media (max-width: 767px) {
      display: none !important;
    }
  }

  .tablet-only {
    @media (max-width: 767px), (min-width: 1024px) {
      display: none !important;
    }
  }
}